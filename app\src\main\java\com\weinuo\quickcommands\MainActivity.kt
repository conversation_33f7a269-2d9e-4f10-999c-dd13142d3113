package com.weinuo.quickcommands

import android.app.ActivityManager
import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Process
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.ui.Alignment
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.compose.material3.Scaffold
import com.weinuo.quickcommands.ui.components.themed.ThemedScaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex

import com.weinuo.quickcommands.ui.effects.HazeManager
import com.weinuo.quickcommands.ui.effects.backgroundBlurSource
import dev.chrisbanes.haze.HazeState
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.weinuo.quickcommands.data.AppRepository
import com.weinuo.quickcommands.data.QuickCommandRepository
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.model.MemoryCheckMode
import com.weinuo.quickcommands.navigation.BottomNavBar
import com.weinuo.quickcommands.navigation.ThemedBottomNavBar
import com.weinuo.quickcommands.navigation.Screen
import com.weinuo.quickcommands.service.QuickCommandsService
import com.weinuo.quickcommands.floating.FloatingButtonManager
import com.weinuo.quickcommands.permission.GlobalPermissionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.weinuo.quickcommands.ui.components.ScrollableAlertDialog
// 旧的界面文件已移动到主题专用目录，现在使用主题感知组件
import com.weinuo.quickcommands.ui.screens.themed.ThemedQuickCommandsScreen
import com.weinuo.quickcommands.ui.screens.themed.ThemedGlobalSettingsScreen
import com.weinuo.quickcommands.ui.screens.themed.ThemedCommandTemplatesScreen
import com.weinuo.quickcommands.ui.screens.themed.ThemedPhoneCheckupScreen
import com.weinuo.quickcommands.ui.screens.themed.ThemedSmartRemindersScreen
import com.weinuo.quickcommands.ui.components.themed.ThemedMainLayout

import com.weinuo.quickcommands.ui.theme.config.BottomNavigationConfig
import com.weinuo.quickcommands.ui.theme.config.NavigationTab
import com.weinuo.quickcommands.shortcut.ShortcutManager

import com.weinuo.quickcommands.ui.screens.DetailConfigurationScreen
import com.weinuo.quickcommands.ui.configuration.CommunicationStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.ConnectionStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.SensorStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.AppStateConfigProvider
import com.weinuo.quickcommands.ui.configuration.BatteryStateConfigProvider

import com.weinuo.quickcommands.ui.configuration.ManualTriggerConfigProvider
import com.weinuo.quickcommands.model.ConnectivityOperation
import com.weinuo.quickcommands.model.ScreenControlOperation
import com.weinuo.quickcommands.model.DeviceSettingsOperation
import com.weinuo.quickcommands.model.ApplicationOperation
import com.weinuo.quickcommands.model.VolumeOperation
import com.weinuo.quickcommands.model.DeviceActionOperation
import com.weinuo.quickcommands.model.DateTimeTaskType
import com.weinuo.quickcommands.model.NotificationOperation
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.storage.NavigationDataStorageManager
import com.weinuo.quickcommands.utils.rememberExperimentalFeatureDetector
import com.weinuo.quickcommands.utils.ExperimentalFeatureDetector
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.NavGraph.Companion.findStartDestination
import com.weinuo.quickcommands.navigation.bottomNavItems
import rikka.shizuku.Shizuku
import com.weinuo.quickcommands.utils.applyAppLanguage

class MainActivity : ComponentActivity() {

    private val shizukuPermissionRequestCode = 1000
    private var shizukuPermissionListener: Shizuku.OnRequestPermissionResultListener? = null

    // 全局权限管理器
    private lateinit var globalPermissionManager: GlobalPermissionManager

    // 权限申请启动器
    private lateinit var communicationPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var locationPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var bluetoothPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var sensorPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var notificationPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var mediaPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var cameraPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var networkPermissionLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var microphonePermissionLauncher: ActivityResultLauncher<Array<String>>

    /**
     * 应用语言设置到Activity
     *
     * 性能优化：
     * 1. 系统默认语言时直接使用原Context，零性能损耗
     * 2. 只在需要时才进行Context包装
     * 3. 使用缓存机制避免重复操作
     * 4. 异常处理确保不影响应用正常启动
     */
    override fun attachBaseContext(newBase: Context) {
        try {
            // 应用语言设置，性能优化版本
            val localizedContext = newBase.applyAppLanguage()
            super.attachBaseContext(localizedContext)

            Log.d("MainActivity", "Language context applied successfully")
        } catch (e: Exception) {
            Log.e("MainActivity", "Error applying language context, using default", e)
            // 出错时使用原Context，确保应用正常启动
            super.attachBaseContext(newBase)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 初始化全局权限管理器
        globalPermissionManager = GlobalPermissionManager.getInstance(this)

        // 初始化权限申请启动器
        initializePermissionLaunchers()

        // 初始化通知渠道
        com.weinuo.quickcommands.utils.NotificationHelper.initializeNotificationChannels(this)

        // 初始化主题性能管理器（确保在主题系统使用前初始化）
        com.weinuo.quickcommands.ui.theme.manager.ThemePerformanceManager.getInstance(this)

        // 初始化语言设置（性能优化：后台线程执行，避免阻塞主线程）
        initializeLanguageSettings()

        // 初始化 Shizuku 权限监听器
        shizukuPermissionListener = Shizuku.OnRequestPermissionResultListener { requestCode, grantResult ->
            if (requestCode == shizukuPermissionRequestCode) {
                if (grantResult == PackageManager.PERMISSION_GRANTED) {
                    // 更新GlobalPermissionManager的权限状态
                    globalPermissionManager.refreshPermissionStates()
                    startBackgroundService()
                } else {
                    // 权限被拒绝时也更新状态
                    globalPermissionManager.refreshPermissionStates()
                }
            }
        }

        setContent {
            QuickCommandsTheme {
                // 初始化数据仓库
                val appRepository = remember { AppRepository(applicationContext) }
                val settingsRepository = remember { SettingsRepository(applicationContext) }

                // 设置 Shizuku 权限监听器
                DisposableEffect(Unit) {
                    Shizuku.addRequestPermissionResultListener(shizukuPermissionListener!!)
                    onDispose {
                        Shizuku.removeRequestPermissionResultListener(shizukuPermissionListener!!)
                    }
                }

                // 检查 Shizuku 状态
                var showShizukuDialog by remember { mutableStateOf(false) }
                var shizukuDialogMessage by remember { mutableStateOf("") }

                // 启动快捷指令服务
                startBackgroundService()

                if (showShizukuDialog) {
                    ScrollableAlertDialog(
                        onDismissRequest = { showShizukuDialog = false },
                        title = stringResource(R.string.shizuku_permission_required),
                        message = shizukuDialogMessage,
                        confirmText = stringResource(R.string.shizuku_install_guide),
                        onConfirm = { showShizukuDialog = false },
                        dismissText = null
                    )
                }

                MainScreen(
                    appRepository = appRepository,
                    settingsRepository = settingsRepository
                )
            }
        }
    }

    /**
     * 启动后台服务（快捷指令功能）
     */
    private fun startBackgroundService() {
        try {
            Log.d("MainActivity", "Starting quick command services...")

            // 检查服务是否已经在运行，避免重复启动
            if (isServiceRunning(QuickCommandsService::class.java)) {
                Log.d("MainActivity", "QuickCommandsService is already running, skipping start")
                return
            }

            // 启动后台服务（快捷指令功能）
            val serviceIntent = Intent(this, QuickCommandsService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
                Log.d("MainActivity", "QuickCommandsService started for quick commands")
            } else {
                startService(serviceIntent)
            }

            // 延迟初始化悬浮按钮管理器，避免同时进行多个初始化操作
            CoroutineScope(Dispatchers.Main).launch {
                delay(1000) // 延迟1秒

                // 初始化悬浮按钮管理器
                try {
                    FloatingButtonManager.initializeFromRepository(this@MainActivity)
                    Log.d("MainActivity", "FloatingButtonManager initialized")
                } catch (e: Exception) {
                    Log.e("MainActivity", "Error initializing FloatingButtonManager", e)
                }

                // 初始化悬浮加速球管理器
                try {
                    com.weinuo.quickcommands.floating.FloatingAcceleratorManager.initializeFromRepository(this@MainActivity)
                    Log.d("MainActivity", "FloatingAcceleratorManager initialized")
                } catch (e: Exception) {
                    Log.e("MainActivity", "Error initializing FloatingAcceleratorManager", e)
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error starting quick command services", e)
        }
    }

    /**
     * 检查指定服务是否正在运行
     */
    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        return try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            @Suppress("DEPRECATION")
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)

            runningServices.any { serviceInfo ->
                serviceInfo.service.className == serviceClass.name
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error checking service status", e)
            false
        }
    }

    override fun onResume() {
        super.onResume()
        // 权限检查现在只在用户主动操作时进行，避免在应用启动时弹出权限对话框

        // 刷新权限状态
        globalPermissionManager.refreshPermissionStates()
    }

    /**
     * 初始化权限申请启动器
     */
    private fun initializePermissionLaunchers() {
        // 通信权限申请启动器
        communicationPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.COMMUNICATION,
                permissions
            )
        }

        // 位置权限申请启动器
        locationPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.LOCATION,
                permissions
            )
        }

        // 蓝牙权限申请启动器
        bluetoothPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.BLUETOOTH,
                permissions
            )
        }

        // 传感器权限申请启动器
        sensorPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.SENSOR,
                permissions
            )
        }

        // 通知权限申请启动器
        notificationPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.NOTIFICATION,
                permissions
            )
        }

        // 媒体权限申请启动器
        mediaPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.MEDIA,
                permissions
            )
        }

        // 相机权限申请启动器
        cameraPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.CAMERA,
                permissions
            )
        }

        // 网络权限申请启动器
        networkPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.NETWORK,
                permissions
            )
        }

        // 麦克风权限申请启动器
        microphonePermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            globalPermissionManager.handlePermissionResult(
                GlobalPermissionManager.PermissionType.MICROPHONE,
                permissions
            )
        }

        // 注册权限申请启动器到全局权限管理器
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.COMMUNICATION,
            communicationPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.LOCATION,
            locationPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.BLUETOOTH,
            bluetoothPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.SENSOR,
            sensorPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.NOTIFICATION,
            notificationPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.MEDIA,
            mediaPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.CAMERA,
            cameraPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.NETWORK,
            networkPermissionLauncher
        )
        globalPermissionManager.registerPermissionLauncher(
            GlobalPermissionManager.PermissionType.MICROPHONE,
            microphonePermissionLauncher
        )
    }

    /**
     * 检查是否有使用情况访问权限
     */
    private fun hasUsageStatsPermission(): Boolean {
        val appOps = getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
        val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            appOps.unsafeCheckOpNoThrow(
                AppOpsManager.OPSTR_GET_USAGE_STATS,
                Process.myUid(),
                packageName
            )
        } else {
            appOps.checkOpNoThrow(
                AppOpsManager.OPSTR_GET_USAGE_STATS,
                Process.myUid(),
                packageName
            )
        }
        return mode == AppOpsManager.MODE_ALLOWED
    }

    /**
     * 打开使用情况访问权限设置页面
     */
    private fun openUsageStatsSettings() {
        try {
            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(
                this,
                getString(R.string.usage_stats_permission_required),
                Toast.LENGTH_LONG
            ).show()
        }
    }

    /**
     * 初始化语言设置
     *
     * 性能优化：
     * 1. 后台线程执行，避免阻塞主线程启动
     * 2. 系统默认语言时几乎零开销
     * 3. 异常处理确保不影响应用正常启动
     */
    private fun initializeLanguageSettings() {
        try {
            // 在后台线程执行语言初始化，避免阻塞主线程
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val settingsRepository = SettingsRepository(applicationContext)
                    val appLanguageManager = com.weinuo.quickcommands.utils.AppLanguageManager(
                        applicationContext,
                        settingsRepository
                    )

                    // 初始化语言设置，确保数据同步
                    appLanguageManager.initializeLanguage()

                    Log.d("MainActivity", "Language settings initialized successfully")
                } catch (e: Exception) {
                    Log.e("MainActivity", "Error initializing language settings", e)
                    // 语言初始化失败不影响应用正常运行
                }
            }
        } catch (e: Exception) {
            Log.e("MainActivity", "Error starting language initialization", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        shizukuPermissionListener?.let {
            Shizuku.removeRequestPermissionResultListener(it)
        }

        // 重置实验性功能的会话状态
        try {
            val settingsRepository = SettingsRepository(applicationContext)
            settingsRepository.resetExperimentalFeaturesSessionState()
        } catch (e: Exception) {
            Log.e("MainActivity", "Error resetting experimental features session state", e)
        }
    }
}

@Composable
fun MainScreen(
    appRepository: AppRepository,
    settingsRepository: SettingsRepository,
    navController: NavHostController = rememberNavController(),
    quickCommandRepository: QuickCommandRepository = QuickCommandRepository.getInstance(navController.context)
) {
    // 获取当前context
    val context = LocalContext.current

    // 创建导航数据存储管理器
    val navigationDataManager = remember { NavigationDataStorageManager(context) }

    // 应用启动时重置实验性功能会话状态
    LaunchedEffect(Unit) {
        settingsRepository.resetExperimentalFeaturesSessionState()
    }

    // 获取当前导航状态
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination
    val currentRoute = currentDestination?.route

    // 实验性功能检测器 - 只在全局设置界面激活
    val experimentalFeatureDetector = rememberExperimentalFeatureDetector(
        isInGlobalSettings = currentRoute == Screen.GlobalSettings.route,
        onActivationCompleted = {
            // 激活完成，只解锁实验性功能，不自动启用
            settingsRepository.unlockExperimentalFeatures()
        }
    )

    // 注意：handleConfigurationComplete函数已被移除
    // 新的Activity架构中，配置完成后的数据传递通过ActivityResult机制处理
    // 不再需要通过NavController进行数据传递

    // 创建HazeManager和HazeState用于iOS风格模糊效果
    val hazeManager = remember { HazeManager.getInstance(context) }
    val hazeState = hazeManager.globalHazeState

    // 创建底部导航配置
    val bottomNavConfig = remember(currentDestination) {
        val tabs = bottomNavItems.map { screen ->
            NavigationTab(
                label = context.getString(screen.titleResId),
                icon = screen.unselectedIcon,
                selectedIcon = screen.selectedIcon
            )
        }

        val selectedIndex = bottomNavItems.indexOfFirst { screen ->
            currentDestination?.route == screen.route
        }.takeIf { it >= 0 } ?: 0

        BottomNavigationConfig(
            tabs = tabs,
            selectedIndex = selectedIndex,
            onTabSelected = { index ->
                val selectedScreen = bottomNavItems[index]

                // 如果是全局设置导航项且有实验性功能检测器，处理点击检测
                if (selectedScreen == Screen.GlobalSettings && experimentalFeatureDetector != null) {
                    experimentalFeatureDetector.handleClick(ExperimentalFeatureDetector.ClickTarget.NAVIGATION_ITEM)
                }

                navController.navigate(selectedScreen.route) {
                    // 避免创建多个返回栈
                    popUpTo(navController.graph.findStartDestination().id) {
                        saveState = true
                    }
                    // 避免多次点击创建多个实例
                    launchSingleTop = true
                    // 恢复状态
                    restoreState = true
                }
            }
        )
    }

    // 使用主题感知的主布局组件
    ThemedMainLayout(
        navController = navController,
        bottomNavConfig = bottomNavConfig,
        hazeState = hazeState
    ) { paddingValues ->
        // 主要内容区域
        NavHost(
            navController = navController,
            startDestination = Screen.PhoneCheckup.route,
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues) // 使用主布局提供的padding
        ) {

            composable(Screen.QuickCommands.route) {
                ThemedQuickCommandsScreen(
                    navController = navController,
                    quickCommandRepository = quickCommandRepository,
                    shortcutManager = ShortcutManager(navController.context)
                )
            }

            composable(Screen.CommandTemplates.route) {
                ThemedCommandTemplatesScreen(
                    navController = navController,
                    quickCommandRepository = quickCommandRepository
                )
            }

            composable(Screen.PhoneCheckup.route) {
                ThemedPhoneCheckupScreen()
            }

            composable(Screen.SmartReminders.route) {
                ThemedSmartRemindersScreen(
                    navController = navController
                )
            }

            composable(Screen.GlobalSettings.route) {
                ThemedGlobalSettingsScreen(
                    settingsRepository = settingsRepository,
                    experimentalFeatureDetector = experimentalFeatureDetector
                )
            }

            // 注意：QuickCommandFormScreen路由已被移除
            // 快捷指令表单现在使用独立的QuickCommandFormActivity
            // 请使用QuickCommandFormActivity.startForCreate()或startForEdit()方法

            // 注意：UnifiedConfigurationScreen路由已被移除
            // 统一配置界面现在使用独立的UnifiedConfigurationActivity
            // 请使用UnifiedConfigurationActivity.startForTriggerCondition()、startForAbortCondition()或startForTask()方法

            // 注意：DetailedConfigurationScreen路由已被移除
            // 详细配置界面现在使用独立的DetailedConfigurationActivity
            // 请使用DetailedConfigurationActivity.startForConfiguration()方法

            // 注意：ContactSelectionScreen路由已被移除
            // 联系人选择界面现在使用独立的ContactSelectionActivity
            // 请使用ContactSelectionActivity.startForResult()方法

            // 注意：ContactGroupSelectionScreen路由已被移除
            // 联系人分组选择界面现在使用独立的ContactGroupSelectionActivity
            // 请使用ContactGroupSelectionActivity.startForResult()方法

            // 注意：AppSelectionScreen路由已被移除
            // 应用选择界面现在使用独立的AppSelectionActivity
            // 请使用AppSelectionActivity.startForResult()方法
        }
    }
}